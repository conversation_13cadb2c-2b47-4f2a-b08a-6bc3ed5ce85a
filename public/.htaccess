# Deny access to templates (but allow from localhost)
<Files *.ss>
	Order deny,allow
	Deny from all
	Allow from 127.0.0.1
</Files>

# Deny access to INC files
<Files *.inc>
	Order deny,allow
	Deny from all
</Files>

# Deny access to YAML files
<Files *.yml>
	Order deny,allow
	Deny from all
</Files>

# Route errors to static pages automatically generated by SilverStripe
ErrorDocument 404 /assets/error-404.html
ErrorDocument 500 /assets/error-500.html

<IfModule mod_alias.c>
	RedirectMatch 403 /silverstripe-cache(/|$)
	RedirectMatch 403 /vendor(/|$)
	RedirectMatch 403 /composer\.(json|lock)
</IfModule>

<IfModule mod_rewrite.c>
	SetEnv HTTP_MOD_REWRITE On
	RewriteEngine On

	# Enable HTTP Basic authentication workaround for PHP running in CGI mode
	RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

	# Deny access to potentially sensitive files and folders
	RewriteRule ^vendor(/|$) - [F,L,NC]
	RewriteRule ^\.env - [F,L,NC]
	RewriteRule silverstripe-cache(/|$) - [F,L,NC]
	RewriteRule composer\.(json|lock) - [F,L,NC]
	RewriteRule (error|silverstripe|debug)\.log - [F,L,NC]

	# Process through SilverStripe if no file with the requested name exists.
	RewriteCond %{REQUEST_URI} ^(.*)$
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteRule .* index.php
</IfModule>
